<template>
  <view class="user-center-container">
    <!-- 顶部设置按钮 -->
    <view class="header-bar">
      <view class="header-title">我的</view>
      <view class="settings-btn" @click="goSettings">
        <u-icon name="setting" size="20" color="#333"></u-icon>
      </view>
    </view>

    <!-- 用户信息头部 -->
    <view class="user-header">
      <!-- 头像居中显示 -->
      <view class="avatar-section" @click="handleAvatarClick">
        <image class="avatar" :src="getImageUrl(userInfo.avatar) || '/static/default-avatar.png'" mode="aspectFill">
        </image>
      </view>

      <!-- 用户信息 -->
      <view class="user-info">
        <text class="nickname">{{ userInfo.nickName || '未登录' }}</text>
        <text class="phone" v-if="isLoggedIn">{{ formatPhone(userInfo.phonenumber) }}</text>
        <text class="login-tip" v-else @click="goLogin">点击登录</text>
      </view>

      <!-- 用户分数 -->
      <view class="scores-section" v-if="isLoggedIn">
        <view class="score-item">
          <text class="score-value">{{ userScores.trustScore || 0 }}</text>
          <text class="score-label">信任分</text>
        </view>
        <view class="score-item">
          <text class="score-value">{{ userScores.executeScore || 0 }}</text>
          <text class="score-label">执行分</text>
        </view>
      </view>

      <!-- 财务信息 -->
      <view class="finance-section" v-if="isLoggedIn">
        <view class="finance-item">
          <text class="finance-value">¥{{ formatMoney(userFinance.deposit || 0) }}</text>
          <text class="finance-label">保障金</text>
        </view>
        <view class="finance-item">
          <text class="finance-value">¥{{ formatMoney(userFinance.commission || 0) }}</text>
          <text class="finance-label">佣金</text>
        </view>
      </view>
    </view>

    <!-- 更多功能 -->
    <view class="more-functions">
      <view class="function-grid">
        <view class="function-item" v-for="func in userCenterFunctions" :key="func.functionId || func.id"
          @click="goFunction(func)">
          <image class="function-icon" :src="getImageUrl(func.functionIcon || func.icon)" mode="aspectFit"></image>
          <text class="function-name">{{ func.functionName || func.name }}</text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" v-if="isLoggedIn">
      <u-button type="error" size="large" @click="logout">退出登录</u-button>
    </view>
  </view>
</template>

<script>
import { getUserInfo, getUserScores, getUserFinance, getUserCenterFunctions } from '@/api/auth'
import { formatMoney } from '@/utils/common'
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      userInfo: {},
      userScores: {},
      userFinance: {},
      userCenterFunctions: []
    }
  },

  computed: {
    isLoggedIn() {
      return !!uni.getStorageSync('token')
    }
  },

  onShow() {
    if (this.isLoggedIn) {
      this.loadUserInfo()
      this.loadUserScores()
      this.loadUserFinance()
    } else {
      this.userInfo = {}
      this.userScores = {}
      this.userFinance = {}
    }
    this.loadUserCenterFunctions()
  },

  methods: {
    // 获取图片完整URL
    getImageUrl,

    async loadUserInfo() {
      try {
        const res = await getUserInfo()
        this.userInfo = res.data || {}
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    async loadUserScores() {
      try {
        const res = await getUserScores()
        this.userScores = res.data || {}
      } catch (error) {
        console.error('加载用户分数失败:', error)
        // 设置默认值
        this.userScores = {
          trustScore: 0,
          executeScore: 0
        }
      }
    },

    async loadUserFinance() {
      try {
        const res = await getUserFinance()
        this.userFinance = res.data || {}
      } catch (error) {
        console.error('加载用户财务信息失败:', error)
        // 设置默认值
        this.userFinance = {
          deposit: 0,
          commission: 0
        }
      }
    },

    async loadUserCenterFunctions() {
      try {
        const res = await getUserCenterFunctions()
        if (res.data && Array.isArray(res.data)) {
          this.userCenterFunctions = res.data
        }
      } catch (error) {
        console.error('加载用户中心功能配置失败:', error)
        // 如果加载失败，使用默认配置
        this.userCenterFunctions = [
          { functionId: 1, functionName: '我的钱包', functionIcon: '/static/icons/wallet.png', functionUrl: '/pages/wallet/index' },
          { functionId: 2, functionName: '我的任务', functionIcon: '/static/icons/task.png', functionUrl: '/pages/task/my' },
          { functionId: 3, functionName: '实名认证', functionIcon: '/static/icons/auth.png', functionUrl: '/pages/user/auth' },
          { functionId: 4, functionName: '客服中心', functionIcon: '/static/icons/service.png', functionUrl: '/pages/service/index' }
        ]
      }
    },

    // 处理头像点击
    handleAvatarClick() {
      if (!this.isLoggedIn) {
        this.goLogin()
      } else {
        uni.navigateTo({
          url: '/pages/user/profile'
        })
      }
    },

    // 跳转到登录页面
    goLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },

    // 跳转到设置页面
    goSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      })
    },

    // 格式化手机号
    formatPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },

    // 跳转到功能页面
    goFunction(func) {
      const url = func.functionUrl || func.url
      if (url) {
        if (func.functionType === '1') {
          // 外部链接
          uni.navigateTo({
            url: '/pages/webview/index?url=' + encodeURIComponent(url)
          })
        } else {
          // 内部页面
          if (!this.isLoggedIn && this.needLogin(url)) {
            this.showLoginTip()
            return
          }
          uni.navigateTo({
            url: url
          })
        }
      }
    },

    // 判断是否需要登录
    needLogin(url) {
      const loginRequiredPages = [
        '/pages/wallet/index',
        '/pages/task/my',
        '/pages/user/auth'
      ]
      return loginRequiredPages.some(page => url.includes(page))
    },

    showLoginTip() {
      uni.showModal({
        title: '提示',
        content: '请先登录',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
    },

    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.user-center-container {
  min-height: 100vh;
  background: #f8f8f8;
}

// 顶部标题栏
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;

  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .settings-btn {
    padding: 10rpx;

    &:active {
      opacity: 0.7;
    }
  }
}

// 用户信息头部
.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  text-align: center;

  // 头像区域
  .avatar-section {
    margin-bottom: 30rpx;

    .avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 80rpx;
      border: 6rpx solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
    }
  }

  // 用户信息
  .user-info {
    margin-bottom: 40rpx;

    .nickname {
      display: block;
      font-size: 40rpx;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 15rpx;
    }

    .phone {
      display: block;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }

    .login-tip {
      display: block;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      text-decoration: underline;
    }
  }

  // 分数区域
  .scores-section {
    display: flex;
    justify-content: center;
    gap: 80rpx;
    margin-bottom: 40rpx;

    .score-item {
      text-align: center;

      .score-value {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 10rpx;
      }

      .score-label {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  // 财务信息
  .finance-section {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 30rpx 0;

    .finance-item {
      flex: 1;
      text-align: center;

      .finance-value {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 10rpx;
      }

      .finance-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

// 更多功能区域
.more-functions {
  background: #ffffff;
  margin: 40rpx;
  border-radius: 20rpx;
  padding: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .function-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .function-item {
      width: 23%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40rpx;
      padding: 20rpx 10rpx;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &:active {
        background: #f8f9fa;
        transform: scale(0.95);
      }

      .function-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 15rpx;
      }

      .function-name {
        font-size: 24rpx;
        color: #666;
        text-align: center;
        line-height: 1.3;
      }
    }
  }
}
</style>
