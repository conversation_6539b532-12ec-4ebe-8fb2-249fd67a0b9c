-- APP功能配置管理菜单 SQL
-- 查找浮光管理菜单ID
SELECT @fuguangMenuId := menu_id FROM sys_menu WHERE menu_name = '浮光管理' AND parent_id = 0;

-- 如果浮光管理菜单不存在，则创建
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '浮光管理', '0', '5', 'fuguang', null, 1, 0, 'M', '0', '0', '', 'system', 'admin', sysdate(), '', null, '浮光APP管理目录'
WHERE NOT EXISTS (SELECT 1 FROM sys_menu WHERE menu_name = '浮光管理' AND parent_id = 0);

-- 重新获取浮光管理菜单ID
SELECT @fuguangMenuId := menu_id FROM sys_menu WHERE menu_name = '浮光管理' AND parent_id = 0;

-- 添加APP功能配置管理菜单
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('APP功能配置', @fuguangMenuId, '3', 'function', 'fuguang/function/index', 1, 0, 'C', '0', '0', 'fuguang:function:list', 'component', 'admin', sysdate(), '', null, 'APP功能配置菜单');

-- 获取APP功能配置菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮父菜单ID
-- 查询APP功能配置按钮
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('APP功能配置查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'fuguang:function:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('APP功能配置新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'fuguang:function:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('APP功能配置修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'fuguang:function:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('APP功能配置删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'fuguang:function:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('APP功能配置导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'fuguang:function:export',       '#', 'admin', sysdate(), '', null, '');

-- 添加字典类型
insert into sys_dict_type values(100, 'APP功能类型', 'app_function_type', '0', 'admin', sysdate(), '', null, 'APP功能类型列表');

-- 添加字典数据
insert into sys_dict_data values(100, 1, '内部页面', '0', 'app_function_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '内部页面');
insert into sys_dict_data values(101, 2, '外部链接', '1', 'app_function_type', '', '', 'Y', '0', 'admin', sysdate(), '', null, '外部链接');
