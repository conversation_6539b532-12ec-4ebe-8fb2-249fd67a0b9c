package com.ruoyi.app.controller;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.fuguang.domain.AppUser;
import com.ruoyi.fuguang.domain.AppFunction;
import com.ruoyi.fuguang.service.IAppUserService;
import com.ruoyi.fuguang.service.IAppFunctionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.Map;

/**
 * APP用户个人信息
 *
 * <AUTHOR>
 */
@Api(tags = "APP用户个人信息")
@RestController("appUserApiController")
@RequestMapping("/app/user")
public class AppUserController extends BaseController
{
    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private IAppFunctionService appFunctionService;

    @Autowired
    private ServerConfig serverConfig;

    /**
     * 个人信息
     */
    @ApiOperation("获取个人信息")
    @GetMapping("/profile")
    public AjaxResult profile()
    {
        Long userId = getUserId();
        AppUser user = appUserService.selectAppUserByUserId(userId);
        if (StringUtils.isNotNull(user))
        {
            user.setPassword(null);
            return success(user);
        }
        return error("用户信息不存在");
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改个人信息")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody AppUser user)
    {
        Long userId = getUserId();
        user.setUserId(userId);
        
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !appUserService.checkPhoneUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !appUserService.checkEmailUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        
        if (appUserService.updateUserProfile(user) > 0)
        {
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @ApiOperation("重置密码")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword)
    {
        Long userId = getUserId();
        AppUser user = appUserService.selectAppUserByUserId(userId);
        String userName = user.getUserName();
        String password = user.getPassword();
        
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }
        
        if (appUserService.resetUserPwd(userName, newPassword) > 0)
        {
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @ApiOperation("头像上传")
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception
    {
        if (!file.isEmpty())
        {
            Long userId = getUserId();
            AppUser user = appUserService.selectAppUserByUserId(userId);
            String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file);
            if (appUserService.updateUserAvatar(user.getUserName(), avatar))
            {
                AjaxResult ajax = AjaxResult.success();
                // 返回完整的图片URL，包含服务器地址
                String fullUrl = serverConfig.getUrl() + avatar;
                ajax.put("imgUrl", fullUrl);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }

    /**
     * 实名认证
     */
    @ApiOperation("实名认证")
    @Log(title = "实名认证", businessType = BusinessType.UPDATE)
    @PostMapping("/auth")
    public AjaxResult realNameAuth(@RequestBody AppUser user)
    {
        Long userId = getUserId();
        
        if (StringUtils.isEmpty(user.getRealName()) || StringUtils.isEmpty(user.getIdCard()))
        {
            return error("真实姓名和身份证号不能为空");
        }
        
        // TODO: 调用第三方实名认证接口验证身份信息
        
        AppUser updateUser = new AppUser();
        updateUser.setUserId(userId);
        updateUser.setRealName(user.getRealName());
        updateUser.setIdCard(user.getIdCard());
        updateUser.setAuthStatus("1"); // 设置为已认证
        
        if (appUserService.updateUserProfile(updateUser) > 0)
        {
            return success("实名认证成功");
        }
        return error("实名认证失败，请联系管理员");
    }

    /**
     * 更新位置信息
     */
    @ApiOperation("更新位置信息")
    @PostMapping("/location")
    public AjaxResult updateLocation(@RequestBody AppUser user)
    {
        Long userId = getUserId();
        
        AppUser updateUser = new AppUser();
        updateUser.setUserId(userId);
        updateUser.setLongitude(user.getLongitude());
        updateUser.setLatitude(user.getLatitude());
        updateUser.setAddress(user.getAddress());
        
        if (appUserService.updateUserProfile(updateUser) > 0)
        {
            return success("位置更新成功");
        }
        return error("位置更新失败");
    }

    /**
     * 检查用户名是否唯一
     */
    @ApiOperation("检查用户名是否唯一")
    @GetMapping("/checkUserNameUnique")
    public AjaxResult checkUserNameUnique(String userName)
    {
        AppUser user = new AppUser();
        user.setUserName(userName);
        boolean unique = appUserService.checkUserNameUnique(user);
        return success(unique);
    }

    /**
     * 检查手机号是否唯一
     */
    @ApiOperation("检查手机号是否唯一")
    @GetMapping("/checkPhoneUnique")
    public AjaxResult checkPhoneUnique(String phonenumber)
    {
        AppUser user = new AppUser();
        user.setPhonenumber(phonenumber);
        boolean unique = appUserService.checkPhoneUnique(user);
        return success(unique);
    }

    /**
     * 检查邮箱是否唯一
     */
    @ApiOperation("检查邮箱是否唯一")
    @GetMapping("/checkEmailUnique")
    public AjaxResult checkEmailUnique(String email)
    {
        AppUser user = new AppUser();
        user.setEmail(email);
        boolean unique = appUserService.checkEmailUnique(user);
        return success(unique);
    }

    /**
     * 获取用户分数信息
     */
    @ApiOperation("获取用户分数信息")
    @GetMapping("/scores")
    public AjaxResult getUserScores()
    {
        Long userId = getUserId();
        // TODO: 从数据库获取用户分数信息
        Map<String, Object> scores = new HashMap<>();
        scores.put("trustScore", 85); // 信任分
        scores.put("executeScore", 92); // 执行分
        return success(scores);
    }

    /**
     * 获取用户财务信息
     */
    @ApiOperation("获取用户财务信息")
    @GetMapping("/finance")
    public AjaxResult getUserFinance()
    {
        Long userId = getUserId();
        // TODO: 从数据库获取用户财务信息
        Map<String, Object> finance = new HashMap<>();
        finance.put("deposit", 500.00); // 保障金
        finance.put("commission", 128.50); // 佣金
        return success(finance);
    }

    /**
     * 获取用户中心功能配置
     */
    @Anonymous
    @ApiOperation("获取用户中心功能配置")
    @GetMapping("/functions")
    public AjaxResult getUserCenterFunctions()
    {
        // 获取用户中心专用的功能配置，与首页功能区分
        // 这里暂时返回所有启用的功能，后续可以通过字段来区分功能类型
        return success(appFunctionService.selectEnabledAppFunctionList());
    }
}
